{"rustc": 5357548097637079788, "features": "[\"barrier\", \"default\", \"lazy\", \"lock_api\", \"lock_api_crate\", \"mutex\", \"once\", \"rwlock\", \"spin_mutex\"]", "declared_features": "[\"barrier\", \"default\", \"fair_mutex\", \"lazy\", \"lock_api\", \"lock_api_crate\", \"mutex\", \"once\", \"portable-atomic\", \"portable_atomic\", \"rwlock\", \"spin_mutex\", \"std\", \"ticket_mutex\", \"use_ticket_mutex\"]", "target": 4260413527236709406, "profile": 8276155916380437441, "path": 18261407390185903098, "deps": [[8081351675046095464, "lock_api_crate", false, 12139912828303043617]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/spin-582b787744efb4de/dep-lib-spin", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}