{"rustc": 5357548097637079788, "features": "[\"alloc\", \"default\", \"fs\", \"std\"]", "declared_features": "[\"all-apis\", \"alloc\", \"compiler_builtins\", \"core\", \"default\", \"event\", \"fs\", \"io_uring\", \"libc\", \"libc_errno\", \"linux_4_11\", \"linux_5_1\", \"linux_5_11\", \"linux_latest\", \"mm\", \"mount\", \"net\", \"param\", \"pipe\", \"process\", \"pty\", \"rand\", \"runtime\", \"rustc-dep-of-std\", \"rustc-std-workspace-alloc\", \"shm\", \"std\", \"stdio\", \"system\", \"termios\", \"thread\", \"time\", \"try_close\", \"use-explicitly-provided-auxv\", \"use-libc\", \"use-libc-auxv\"]", "target": 16221545317719767766, "profile": 2210360036522109082, "path": 9591283258486253787, "deps": [[4684437522915235464, "libc", false, 5965025303721939229], [7896293946984509699, "bitflags", false, 17171064432276737338], [8253628577145923712, "libc_errno", false, 12017206800885275398], [12053020504183902936, "build_script_build", false, 11356502263182101968]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/rustix-799d0c94002db7cb/dep-lib-rustix", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}