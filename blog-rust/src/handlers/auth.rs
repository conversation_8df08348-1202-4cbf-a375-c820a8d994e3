use axum::{
    extract::{Extension, State},
    http::StatusCode,
    response::Json,
    routing::{get, post},
    Router,
};
use bcrypt::{hash, verify, DEFAULT_COST};
use chrono::{Duration, Utc};
use jsonwebtoken::{encode, Enco<PERSON><PERSON><PERSON>, Header};
use serde_json::json;

use crate::{
    error::AppError,
    models::{AuthResponse, Claims, Create<PERSON>ser, LoginUser, User},
    AppState,
};

pub fn routes() -> Router<AppState> {
    Router::new()
        .route("/register", post(register))
        .route("/login", post(login))
        .route("/me", get(get_current_user))
}

async fn register(
    State(state): State<AppState>,
    Json(payload): Json<CreateUser>,
) -> Result<Json<AuthResponse>, AppError> {
    // 验证输入
    if payload.username.trim().is_empty() {
        return Err(AppError::Validation("Username cannot be empty".to_string()));
    }
    if payload.email.trim().is_empty() {
        return Err(AppError::Validation("Email cannot be empty".to_string()));
    }
    if payload.password.len() < 6 {
        return Err(AppError::Validation("Password must be at least 6 characters".to_string()));
    }

    // 检查用户是否已存在
    let existing_user = sqlx::query_as::<_, User>(
        "SELECT * FROM users WHERE email = ? OR username = ?"
    )
    .bind(&payload.email)
    .bind(&payload.username)
    .fetch_optional(&state.db.pool)
    .await?;

    if existing_user.is_some() {
        return Err(AppError::BadRequest("User already exists".to_string()));
    }

    // 哈希密码
    let password_hash = hash(payload.password, DEFAULT_COST)?;

    // 创建用户
    let user_id = sqlx::query(
        "INSERT INTO users (username, email, password_hash) VALUES (?, ?, ?)"
    )
    .bind(&payload.username)
    .bind(&payload.email)
    .bind(&password_hash)
    .execute(&state.db.pool)
    .await?
    .last_insert_rowid();

    // 获取创建的用户
    let user = sqlx::query_as::<_, User>("SELECT * FROM users WHERE id = ?")
        .bind(user_id)
        .fetch_one(&state.db.pool)
        .await?;

    // 生成JWT token
    let token = generate_token(&user, &state.config.jwt_secret)?;

    Ok(Json(AuthResponse { token, user }))
}

async fn login(
    State(state): State<AppState>,
    Json(payload): Json<LoginUser>,
) -> Result<Json<AuthResponse>, AppError> {
    // 查找用户
    let user = sqlx::query_as::<_, User>("SELECT * FROM users WHERE email = ?")
        .bind(&payload.email)
        .fetch_optional(&state.db.pool)
        .await?
        .ok_or_else(|| AppError::Authentication("Invalid credentials".to_string()))?;

    // 验证密码
    if !verify(&payload.password, &user.password_hash)? {
        return Err(AppError::Authentication("Invalid credentials".to_string()));
    }

    // 生成JWT token
    let token = generate_token(&user, &state.config.jwt_secret)?;

    Ok(Json(AuthResponse { token, user }))
}

async fn get_current_user(
    Extension(claims): Extension<Claims>,
    State(state): State<AppState>,
) -> Result<Json<User>, AppError> {
    let user_id: i64 = claims.sub.parse()
        .map_err(|_| AppError::Authentication("Invalid user ID in token".to_string()))?;

    let user = sqlx::query_as::<_, User>("SELECT * FROM users WHERE id = ?")
        .bind(user_id)
        .fetch_optional(&state.db.pool)
        .await?
        .ok_or_else(|| AppError::NotFound("User not found".to_string()))?;

    Ok(Json(user))
}

fn generate_token(user: &User, secret: &str) -> Result<String, AppError> {
    let expiration = Utc::now()
        .checked_add_signed(Duration::days(7))
        .expect("valid timestamp")
        .timestamp() as usize;

    let claims = Claims {
        sub: user.id.to_string(),
        username: user.username.clone(),
        exp: expiration,
    };

    let token = encode(
        &Header::default(),
        &claims,
        &EncodingKey::from_secret(secret.as_ref()),
    )?;

    Ok(token)
}
