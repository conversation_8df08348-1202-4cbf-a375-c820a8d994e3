fn(axum::extract::Path<i64>, Extension<Claims>, axum::extract::State<AppState>, axum::Json<UpdatePost>) -> impl std::future::Future<Output = Result<axum::Json<Post>, AppError>> {update_post}
fn(axum::extract::Path<i64>, Extension<Claims>, axum::extract::State<AppState>, axum::Json<UpdatePost>) -> impl std::future::Future<Output = Result<axum::Json<Post>, AppError>> {update_post}: Handler<_, AppState>
