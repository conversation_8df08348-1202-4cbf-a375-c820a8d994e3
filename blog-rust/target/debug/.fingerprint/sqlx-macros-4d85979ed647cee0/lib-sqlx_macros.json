{"rustc": 5357548097637079788, "features": "[\"_rt-tokio\", \"_tls-rustls\", \"chrono\", \"default\", \"json\", \"migrate\", \"sqlite\", \"uuid\"]", "declared_features": "[\"_rt-async-std\", \"_rt-tokio\", \"_tls-native-tls\", \"_tls-rustls\", \"bigdecimal\", \"bit-vec\", \"chrono\", \"default\", \"ipnetwork\", \"json\", \"mac_address\", \"migrate\", \"mysql\", \"postgres\", \"rust_decimal\", \"sqlite\", \"time\", \"uuid\"]", "target": 13494433325021527976, "profile": 3033921117576893, "path": 15181416218038543152, "deps": [[996810380461694889, "sqlx_core", false, 12323985099498665696], [2713742371683562785, "syn", false, 10012914129646168193], [3060637413840920116, "proc_macro2", false, 1754678614257983350], [15733334431800349573, "sqlx_macros_core", false, 8235995702003236127], [17990358020177143287, "quote", false, 464350311428650664]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/sqlx-macros-4d85979ed647cee0/dep-lib-sqlx_macros", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}