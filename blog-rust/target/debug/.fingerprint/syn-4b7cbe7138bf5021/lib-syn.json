{"rustc": 5357548097637079788, "features": "[\"clone-impls\", \"derive\", \"full\", \"parsing\", \"printing\", \"proc-macro\", \"quote\"]", "declared_features": "[\"clone-impls\", \"default\", \"derive\", \"extra-traits\", \"fold\", \"full\", \"parsing\", \"printing\", \"proc-macro\", \"quote\", \"test\", \"visit\", \"visit-mut\"]", "target": 11103975901103234717, "profile": 3033921117576893, "path": 8924772517960723923, "deps": [[1988483478007900009, "unicode_ident", false, 13346426457492500866], [2713742371683562785, "build_script_build", false, 15425177783924625490], [3060637413840920116, "proc_macro2", false, 1754678614257983350], [17990358020177143287, "quote", false, 464350311428650664]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/syn-4b7cbe7138bf5021/dep-lib-syn", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}