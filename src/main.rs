use axum::{
    extract::State,
    http::StatusCode,
    response::Json,
    routing::{get, post},
    Router,
};
use serde::{Deserialize, Serialize};
use sqlx::{sqlite::SqlitePool, Row};
use std::sync::Arc;
use tower_http::cors::CorsLayer;
use tracing::{info, warn};

mod config;
mod database;
mod handlers;
mod models;
mod middleware;
mod error;

use config::Config;
use database::Database;
use error::AppError;

#[derive(Clone)]
pub struct AppState {
    pub db: Database,
    pub config: Config,
}

#[derive(Serialize)]
struct HealthResponse {
    status: String,
    message: String,
}

async fn health_check() -> Result<Json<HealthResponse>, AppError> {
    Ok(Json(HealthResponse {
        status: "ok".to_string(),
        message: "Blog API is running".to_string(),
    }))
}

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 初始化日志
    tracing_subscriber::fmt::init();

    // 加载配置
    let config = Config::new()?;
    info!("Configuration loaded successfully");

    // 初始化数据库
    let database = Database::new(&config.database_url).await?;
    info!("Database connected successfully");

    // 运行数据库迁移
    database.migrate().await?;
    info!("Database migrations completed");

    // 创建应用状态
    let app_state = AppState {
        db: database,
        config: config.clone(),
    };

    // 创建路由
    let app = Router::new()
        .route("/health", get(health_check))
        .nest("/api/auth", handlers::auth::routes())
        .nest("/api/posts", handlers::posts::routes())
        .nest("/api/comments", handlers::comments::routes())
        .nest("/api/upload", handlers::upload::routes())
        .layer(CorsLayer::permissive())
        .with_state(app_state);

    // 启动服务器
    let listener = tokio::net::TcpListener::bind(&config.server_address).await?;
    info!("Server starting on {}", config.server_address);
    
    axum::serve(listener, app).await?;

    Ok(())
}
