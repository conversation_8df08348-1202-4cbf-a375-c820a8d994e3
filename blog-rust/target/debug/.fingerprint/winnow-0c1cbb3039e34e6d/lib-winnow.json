{"rustc": 5357548097637079788, "features": "[\"alloc\", \"default\", \"std\"]", "declared_features": "[\"alloc\", \"debug\", \"default\", \"simd\", \"std\", \"unstable-doc\", \"unstable-recover\"]", "target": 13376497836617006023, "profile": 11259629673856435433, "path": 10363815802807130720, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/winnow-0c1cbb3039e34e6d/dep-lib-winnow", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}