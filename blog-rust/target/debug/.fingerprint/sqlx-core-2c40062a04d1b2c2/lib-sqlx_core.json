{"rustc": 5357548097637079788, "features": "[\"_rt-tokio\", \"_tls-rustls\", \"any\", \"chrono\", \"crc\", \"default\", \"json\", \"migrate\", \"offline\", \"rustls\", \"rustls-pemfile\", \"serde\", \"serde_json\", \"sha2\", \"tokio\", \"tokio-stream\", \"uuid\", \"webpki-roots\"]", "declared_features": "[\"_rt-async-std\", \"_rt-tokio\", \"_tls-native-tls\", \"_tls-none\", \"_tls-rustls\", \"any\", \"async-io\", \"async-std\", \"bigdecimal\", \"bit-vec\", \"bstr\", \"chrono\", \"crc\", \"default\", \"digest\", \"encoding_rs\", \"ipnetwork\", \"json\", \"mac_address\", \"migrate\", \"native-tls\", \"num-bigint\", \"offline\", \"regex\", \"rust_decimal\", \"rustls\", \"rustls-pemfile\", \"serde\", \"serde_json\", \"sha1\", \"sha2\", \"time\", \"tokio\", \"tokio-stream\", \"uuid\", \"webpki-roots\"]", "target": 2042750936636613814, "profile": 3033921117576893, "path": 2052070394144991263, "deps": [[5103565458935487, "futures_io", false, 7965960397150641986], [40386456601120721, "percent_encoding", false, 2035766645680874725], [530211389790465181, "hex", false, 2391823497485890072], [788558663644978524, "crossbeam_queue", false, 13797496514369946937], [966925859616469517, "ahash", false, 7283851370903931570], [1162433738665300155, "crc", false, 16954104232560643041], [1464803193346256239, "event_listener", false, 5241894445502708645], [1811549171721445101, "futures_channel", false, 831035857172615977], [3150220818285335163, "url", false, 5312069282146588504], [3405817021026194662, "hashlink", false, 1539934713652923008], [3646857438214563691, "futures_intrusive", false, 17371596329758392599], [3666196340704888985, "smallvec", false, 8217280242794771746], [3712811570531045576, "byteorder", false, 15815680584661756355], [3722963349756955755, "once_cell", false, 4289841192797198571], [5986029879202738730, "log", false, 11965155188274758339], [7620660491849607393, "futures_core", false, 2545877253845825263], [8008191657135824715, "thiserror", false, 14892438519270743000], [8319709847752024821, "uuid", false, 13019715202406430236], [8606274917505247608, "tracing", false, 15530362375753421347], [9538054652646069845, "tokio", false, 3141952500954974871], [9689903380558560274, "serde", false, 4062869256653540408], [9857275760291862238, "sha2", false, 16245770659194267714], [9897246384292347999, "chrono", false, 6405252837122471257], [10629569228670356391, "futures_util", false, 1690612294085387148], [10862088793507253106, "sqlformat", false, 16314884726313962167], [11295624341523567602, "rustls", false, 17570036238674431755], [12170264697963848012, "either", false, 16120300547085421143], [14483812548788871374, "indexmap", false, 7845253492169564490], [15367738274754116744, "serde_json", false, 12834807164649872498], [15932120279885307830, "memchr", false, 12653815638509802016], [16066129441945555748, "bytes", false, 6648963029734467334], [16311359161338405624, "rustls_pemfile", false, 2602796321245818658], [16973251432615581304, "tokio_stream", false, 14872075054669773412], [17106256174509013259, "atoi", false, 7895389367143506573], [17605717126308396068, "paste", false, 9865687146743202524], [17652733826348741533, "webpki_roots", false, 3499795074268729012]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/sqlx-core-2c40062a04d1b2c2/dep-lib-sqlx_core", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}