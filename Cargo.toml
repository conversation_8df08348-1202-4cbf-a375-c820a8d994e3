[package]
name = "blog-rust"
version = "0.1.0"
edition = "2021"

[dependencies]
# Web框架
axum = "0.7"
tokio = { version = "1.0", features = ["full"] }
tower = "0.4"
tower-http = { version = "0.5", features = ["cors", "fs"] }

# 数据库
sqlx = { version = "0.7", features = ["runtime-tokio-rustls", "sqlite", "chrono", "uuid"] }

# 序列化
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"

# 认证
jsonwebtoken = "9.0"
bcrypt = "0.15"

# 配置和环境
config = "0.14"
dotenv = "0.15"

# 日志
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter"] }

# 时间处理
chrono = { version = "0.4", features = ["serde"] }

# UUID
uuid = { version = "1.0", features = ["v4", "serde"] }

# 错误处理
anyhow = "1.0"
thiserror = "1.0"

# HTTP客户端
reqwest = { version = "0.11", features = ["json"] }
