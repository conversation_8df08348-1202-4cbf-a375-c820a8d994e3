use axum::{
    extract::{Request, State},
    http::{header::AUTH<PERSON><PERSON><PERSON><PERSON><PERSON>, HeaderMap, StatusCode},
    middleware::Next,
    response::Response,
};
use jsonwebtoken::{decode, DecodingKey, Validation};

use crate::{error::AppError, models::Claims, AppState};

pub async fn auth_middleware(
    State(state): State<AppState>,
    headers: HeaderMap,
    mut request: Request,
    next: Next,
) -> Result<Response, AppError> {
    let token = extract_token_from_header(&headers)?;
    let claims = validate_token(&token, &state.config.jwt_secret)?;
    
    // 将用户信息添加到请求扩展中
    request.extensions_mut().insert(claims);
    
    Ok(next.run(request).await)
}

fn extract_token_from_header(headers: &HeaderMap) -> Result<String, AppError> {
    let auth_header = headers
        .get(AUTHORIZATION)
        .ok_or_else(|| AppError::Authentication("Missing authorization header".to_string()))?
        .to_str()
        .map_err(|_| AppError::Authentication("Invalid authorization header".to_string()))?;

    if !auth_header.starts_with("Bearer ") {
        return Err(AppError::Authentication(
            "Authorization header must start with 'Bearer '".to_string(),
        ));
    }

    Ok(auth_header[7..].to_string())
}

fn validate_token(token: &str, secret: &str) -> Result<Claims, AppError> {
    let decoding_key = DecodingKey::from_secret(secret.as_ref());
    let validation = Validation::default();

    let token_data = decode::<Claims>(token, &decoding_key, &validation)?;
    Ok(token_data.claims)
}

// 可选的认证中间件，不会因为缺少token而失败
pub async fn optional_auth_middleware(
    State(state): State<AppState>,
    headers: HeaderMap,
    mut request: Request,
    next: Next,
) -> Response {
    if let Ok(token) = extract_token_from_header(&headers) {
        if let Ok(claims) = validate_token(&token, &state.config.jwt_secret) {
            request.extensions_mut().insert(claims);
        }
    }
    
    next.run(request).await
}
