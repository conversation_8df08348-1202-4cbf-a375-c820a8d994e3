{"rustc": 5357548097637079788, "features": "[\"bundled\", \"bundled_bindings\", \"cc\", \"pkg-config\", \"unlock_notify\", \"vcpkg\"]", "declared_features": "[\"bindgen\", \"buildtime_bindgen\", \"bundled\", \"bundled-sqlcipher\", \"bundled-sqlcipher-vendored-openssl\", \"bundled-windows\", \"bundled_bindings\", \"cc\", \"default\", \"in_gecko\", \"loadable_extension\", \"min_sqlite_version_3_14_0\", \"openssl-sys\", \"pkg-config\", \"prettyplease\", \"preupdate_hook\", \"quote\", \"session\", \"sqlcipher\", \"syn\", \"unlock_notify\", \"vcpkg\", \"wasm32-wasi-vfs\", \"winsqlite3\", \"with-asan\"]", "target": 3421942236757206917, "profile": 3033921117576893, "path": 17217802904769881019, "deps": [[11564048663401192931, "build_script_build", false, 17472546766421585890]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/libsqlite3-sys-7292d6b4f453b62d/dep-lib-libsqlite3_sys", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}