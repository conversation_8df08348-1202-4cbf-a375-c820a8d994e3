{"rustc": 5357548097637079788, "features": "[\"async\", \"async-trait\", \"convert-case\", \"convert_case\", \"default\", \"ini\", \"json\", \"json5\", \"json5_rs\", \"ron\", \"rust-ini\", \"serde_json\", \"toml\", \"yaml\", \"yaml-rust2\"]", "declared_features": "[\"async\", \"async-trait\", \"convert-case\", \"convert_case\", \"default\", \"indexmap\", \"ini\", \"json\", \"json5\", \"json5_rs\", \"preserve_order\", \"ron\", \"rust-ini\", \"serde_json\", \"toml\", \"yaml\", \"yaml-rust2\"]", "target": 4953464226640322992, "profile": 1398949370558892206, "path": 1745132145644311108, "deps": [[1213098572879462490, "json5_rs", false, 13583565305058252398], [1965680986145237447, "yaml_rust2", false, 16147157618568984000], [2244620803250265856, "ron", false, 15270685277741131181], [6502365400774175331, "nom", false, 587500797733811342], [6517602928339163454, "path<PERSON><PERSON>", false, 765919121884433454], [9689903380558560274, "serde", false, 12040820512615725414], [11946729385090170470, "async_trait", false, 7413244485866240106], [13475460906694513802, "convert_case", false, 1033949416706107783], [14618892375165583068, "ini", false, 1091438650952130488], [15367738274754116744, "serde_json", false, 18262523577654186226], [15609422047640926750, "toml", false, 9009200859911417069]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/config-765261fb81038e16/dep-lib-config", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}