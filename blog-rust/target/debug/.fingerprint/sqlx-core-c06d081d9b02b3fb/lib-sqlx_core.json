{"rustc": 5357548097637079788, "features": "[\"_rt-tokio\", \"_tls-rustls\", \"any\", \"chrono\", \"crc\", \"default\", \"json\", \"migrate\", \"offline\", \"rustls\", \"rustls-pemfile\", \"serde\", \"serde_json\", \"sha2\", \"tokio\", \"tokio-stream\", \"uuid\", \"webpki-roots\"]", "declared_features": "[\"_rt-async-std\", \"_rt-tokio\", \"_tls-native-tls\", \"_tls-none\", \"_tls-rustls\", \"any\", \"async-io\", \"async-std\", \"bigdecimal\", \"bit-vec\", \"bstr\", \"chrono\", \"crc\", \"default\", \"digest\", \"encoding_rs\", \"ipnetwork\", \"json\", \"mac_address\", \"migrate\", \"native-tls\", \"num-bigint\", \"offline\", \"regex\", \"rust_decimal\", \"rustls\", \"rustls-pemfile\", \"serde\", \"serde_json\", \"sha1\", \"sha2\", \"time\", \"tokio\", \"tokio-stream\", \"uuid\", \"webpki-roots\"]", "target": 2042750936636613814, "profile": 8276155916380437441, "path": 2052070394144991263, "deps": [[5103565458935487, "futures_io", false, 13704330030595107027], [40386456601120721, "percent_encoding", false, 2749120119751717381], [530211389790465181, "hex", false, 12262939169730878395], [788558663644978524, "crossbeam_queue", false, 12630706898897548493], [966925859616469517, "ahash", false, 2364605029338257745], [1162433738665300155, "crc", false, 6554031639560385436], [1464803193346256239, "event_listener", false, 5505728526376565131], [1811549171721445101, "futures_channel", false, 4047793173850915469], [3150220818285335163, "url", false, 12352002792573394206], [3405817021026194662, "hashlink", false, 17907517798485097532], [3646857438214563691, "futures_intrusive", false, 10875918549134268722], [3666196340704888985, "smallvec", false, 16892299403482249415], [3712811570531045576, "byteorder", false, 3067390179546784019], [3722963349756955755, "once_cell", false, 18084365406079319892], [5986029879202738730, "log", false, 10057176828493275753], [7620660491849607393, "futures_core", false, 5788018040967411665], [8008191657135824715, "thiserror", false, 9386449276996006552], [8319709847752024821, "uuid", false, 13546330315390831644], [8606274917505247608, "tracing", false, 12224643859909459637], [9538054652646069845, "tokio", false, 56344163144673245], [9689903380558560274, "serde", false, 12040820512615725414], [9857275760291862238, "sha2", false, 6494468053886777166], [9897246384292347999, "chrono", false, 8783870760920602813], [10629569228670356391, "futures_util", false, 14048675563864032644], [10862088793507253106, "sqlformat", false, 12681602291771450938], [11295624341523567602, "rustls", false, 12151701839090797260], [12170264697963848012, "either", false, 14691881016359936506], [14483812548788871374, "indexmap", false, 2717209490033362132], [15367738274754116744, "serde_json", false, 18262523577654186226], [15932120279885307830, "memchr", false, 14853395366634842422], [16066129441945555748, "bytes", false, 9168396179284048288], [16311359161338405624, "rustls_pemfile", false, 5419140604684099351], [16973251432615581304, "tokio_stream", false, 9000037818236442941], [17106256174509013259, "atoi", false, 535779462906346797], [17605717126308396068, "paste", false, 9865687146743202524], [17652733826348741533, "webpki_roots", false, 592336378527132096]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/sqlx-core-c06d081d9b02b3fb/dep-lib-sqlx_core", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}