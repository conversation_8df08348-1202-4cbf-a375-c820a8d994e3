fn(axum::extract::Path<i64>, Extension<Claims>, axum::extract::State<AppState>, axum::<PERSON><PERSON><CreateComment>) -> impl std::future::Future<Output = Result<axum::J<PERSON><Comment>, AppError>> {create_comment}
fn(axum::extract::Path<i64>, Extension<Claims>, axum::extract::State<AppState>, axum::Json<CreateComment>) -> impl std::future::Future<Output = Result<axum::Json<Comment>, AppError>> {create_comment}: Handler<_, AppState>
