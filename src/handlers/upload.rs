use axum::{
    extract::{Multipart, State},
    http::StatusCode,
    response::Json,
    routing::post,
    Router,
};
use serde::Serialize;
use std::{fs, path::Path};
use tokio::io::AsyncWriteExt;
use uuid::Uuid;

use crate::{error::AppError, AppState};

#[derive(Serialize)]
struct UploadResponse {
    url: String,
    filename: String,
}

pub fn routes() -> Router<AppState> {
    Router::new().route("/", post(upload_file))
}

async fn upload_file(
    State(state): State<AppState>,
    mut multipart: Multipart,
) -> Result<Json<UploadResponse>, AppError> {
    // 确保上传目录存在
    fs::create_dir_all(&state.config.upload_dir)?;

    while let Some(field) = multipart.next_field().await.map_err(|e| {
        AppError::BadRequest(format!("Failed to read multipart field: {}", e))
    })? {
        let name = field.name().unwrap_or("").to_string();
        let filename = field.file_name().unwrap_or("").to_string();

        if name == "file" && !filename.is_empty() {
            let data = field.bytes().await.map_err(|e| {
                AppError::BadRequest(format!("Failed to read file data: {}", e))
            })?;

            // 检查文件大小
            if data.len() > state.config.max_file_size {
                return Err(AppError::BadRequest("File too large".to_string()));
            }

            // 验证文件类型
            let extension = Path::new(&filename)
                .extension()
                .and_then(|ext| ext.to_str())
                .unwrap_or("");

            let allowed_extensions = ["jpg", "jpeg", "png", "gif", "webp", "svg"];
            if !allowed_extensions.contains(&extension.to_lowercase().as_str()) {
                return Err(AppError::BadRequest("Invalid file type".to_string()));
            }

            // 生成唯一文件名
            let unique_filename = format!("{}_{}.{}", Uuid::new_v4(), filename, extension);
            let file_path = Path::new(&state.config.upload_dir).join(&unique_filename);

            // 保存文件
            let mut file = tokio::fs::File::create(&file_path).await?;
            file.write_all(&data).await?;

            let url = format!("/uploads/{}", unique_filename);

            return Ok(Json(UploadResponse {
                url,
                filename: unique_filename,
            }));
        }
    }

    Err(AppError::BadRequest("No file found in request".to_string()))
}
