use axum::{
    extract::{Extension, Path, State},
    http::StatusC<PERSON>,
    response::Json,
    routing::{delete, get, post},
    Router,
};

use crate::{
    error::AppError,
    models::{<PERSON><PERSON><PERSON>, Comment, CommentWithAuthor, CreateComment, User},
    AppState,
};

pub fn routes() -> Router<AppState> {
    Router::new()
        .route("/:post_id", get(get_comments).post(create_comment))
        .route("/comment/:id", delete(delete_comment))
}

async fn get_comments(
    Path(post_id): Path<i64>,
    State(state): State<AppState>,
) -> Result<Json<Vec<CommentWithAuthor>>, AppError> {
    let comments = sqlx::query_as::<_, Comment>(
        "SELECT * FROM comments WHERE post_id = ? ORDER BY created_at ASC"
    )
    .bind(post_id)
    .fetch_all(&state.db.pool)
    .await?;

    let mut result = Vec::new();
    for comment in comments {
        let author = sqlx::query_as::<_, User>("SELECT * FROM users WHERE id = ?")
            .bind(comment.author_id)
            .fetch_one(&state.db.pool)
            .await?;

        result.push(CommentWithAuthor { comment, author });
    }

    Ok(Json(result))
}

async fn create_comment(
    Path(post_id): Path<i64>,
    Extension(claims): Extension<Claims>,
    State(state): State<AppState>,
    Json(payload): Json<CreateComment>,
) -> Result<Json<Comment>, AppError> {
    let author_id: i64 = claims.sub.parse()
        .map_err(|_| AppError::Authentication("Invalid user ID in token".to_string()))?;

    if payload.content.trim().is_empty() {
        return Err(AppError::Validation("Comment content cannot be empty".to_string()));
    }

    // 检查文章是否存在
    let post_exists = sqlx::query_scalar::<_, bool>("SELECT EXISTS(SELECT 1 FROM posts WHERE id = ?)")
        .bind(post_id)
        .fetch_one(&state.db.pool)
        .await?;

    if !post_exists {
        return Err(AppError::NotFound("Post not found".to_string()));
    }

    let comment_id = sqlx::query(
        "INSERT INTO comments (post_id, author_id, content) VALUES (?, ?, ?)"
    )
    .bind(post_id)
    .bind(author_id)
    .bind(&payload.content)
    .execute(&state.db.pool)
    .await?
    .last_insert_rowid();

    let comment = sqlx::query_as::<_, Comment>("SELECT * FROM comments WHERE id = ?")
        .bind(comment_id)
        .fetch_one(&state.db.pool)
        .await?;

    Ok(Json(comment))
}

async fn delete_comment(
    Path(id): Path<i64>,
    Extension(claims): Extension<Claims>,
    State(state): State<AppState>,
) -> Result<StatusCode, AppError> {
    let author_id: i64 = claims.sub.parse()
        .map_err(|_| AppError::Authentication("Invalid user ID in token".to_string()))?;

    let result = sqlx::query("DELETE FROM comments WHERE id = ? AND author_id = ?")
        .bind(id)
        .bind(author_id)
        .execute(&state.db.pool)
        .await?;

    if result.rows_affected() == 0 {
        return Err(AppError::NotFound("Comment not found or not authorized".to_string()));
    }

    Ok(StatusCode::NO_CONTENT)
}
