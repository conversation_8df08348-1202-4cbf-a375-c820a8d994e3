{"rustc": 5357548097637079788, "features": "[\"alloc\", \"ansi\", \"default\", \"env-filter\", \"fmt\", \"matchers\", \"nu-ansi-term\", \"once_cell\", \"regex\", \"registry\", \"sharded-slab\", \"smallvec\", \"std\", \"thread_local\", \"tracing\", \"tracing-log\"]", "declared_features": "[\"alloc\", \"ansi\", \"chrono\", \"default\", \"env-filter\", \"fmt\", \"json\", \"local-time\", \"matchers\", \"nu-ansi-term\", \"once_cell\", \"parking_lot\", \"regex\", \"registry\", \"serde\", \"serde_json\", \"sharded-slab\", \"smallvec\", \"std\", \"thread_local\", \"time\", \"tracing\", \"tracing-log\", \"tracing-serde\", \"valuable\", \"valuable-serde\", \"valuable_crate\"]", "target": 4817557058868189149, "profile": 3992724396554112236, "path": 7751351066114246013, "deps": [[1009387600818341822, "matchers", false, 12746559681348334143], [1017461770342116999, "sharded_slab", false, 12233027272673306704], [1359731229228270592, "thread_local", false, 2720725157600751899], [3424551429995674438, "tracing_core", false, 11025187502664897511], [3666196340704888985, "smallvec", false, 16892299403482249415], [3722963349756955755, "once_cell", false, 18084365406079319892], [8606274917505247608, "tracing", false, 12224643859909459637], [8614575489689151157, "nu_ansi_term", false, 1719537830547205451], [9451456094439810778, "regex", false, 13774595010040336701], [10806489435541507125, "tracing_log", false, 5280430964700514093]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tracing-subscriber-a0fe4b0e35185bcd/dep-lib-tracing_subscriber", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}